import { SinupComponent } from './sinup/sinup.component';
import { LogComponent } from './log/log.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { Routes } from '@angular/router';

import { UpdateComponent } from './patient-management/update/update.component';
import { ViewRecordsComponent } from './medical-records-handling/view-records/view-records.component';
import { ViewReportsComponent } from './medical-records-handling/view-reports/view-reports.component';
import { AddRecordsComponent } from './medical-records-handling/add-records/add-records.component';
import { DashBordPageComponent } from './dash-bord-page/dash-bord-page.component';
import { ViewPatientListComponent } from './patient-management/view-patient-list/view-patient-list.component';
import { PatientManagementComponent } from './patient-management/patient-management.component';
import { SearchComponent } from './patient-management/search/search.component';
import { AddComponent } from './patient-management/add/add.component';
import { AddAppointmentComponent } from './appointment-handling/add-appointment/add-appointment.component';
import { DeleteAppointmentComponent } from './appointment-handling/delete-appointment/delete-appointment.component';
import { AppointmentViewComponent } from './appointment-handling/appointment-view/appointment-view.component';
import { AppoimentSearchComponent } from './appointment-handling/appoiment-search/appoiment-search.component';
import { PrescriptionManagementComponent } from './prescription-management/prescription-management.component';
import { UpdateAppointmentComponent } from './appointment-handling/update-appointment/update-appointment.component';
import { AddCategoryComponent } from './appointment-handling/add-category/add-category.component'
import { UpdateCategoryComponent } from './appointment-handling/update-category/update-category.component'
import { ViewCategoryComponent } from './appointment-handling/view-category/view-category.component'
import { DoctorsComponent } from './doctors/./doctors.component'
import { SettingsComponent } from './settings/./settings.component'

export const routes: Routes = [

  { path: 'log', component: LogComponent },
    { path: 'sinup', component: SinupComponent },


    {
        path: '',
        component: DashBordPageComponent,
        children: [
            {
                path: "view-patient",
                component: ViewPatientListComponent
            },
            {
                path: "search-patient",
                component: SearchComponent
            },
            {
                path: "add-patient",
                component: AddComponent
            },
            {
                path: "update-patient",
                component: UpdateComponent
            },
            {
                path: "add-appointment",
                component: AddAppointmentComponent
            },
            {
                path: "delete-appointment",
                component: DeleteAppointmentComponent
            },
            {
                path: "update-appointment",
                component: UpdateAppointmentComponent
            },
            {
                path: "view-appointment",
                component: AppointmentViewComponent
            },
            {
                path: "search-appointment",
                component: AppoimentSearchComponent
            },
            {
                path: "prescription-management",
                component: PrescriptionManagementComponent
            },
            {
                path: "add-category",
                component: AddCategoryComponent
            },
            {
                path: "update-category",
                component: UpdateCategoryComponent
            },
            {
                path: "view-category",
                component: ViewCategoryComponent
            },
            {
                path: "view-records",
                component: ViewRecordsComponent
            },
            {
                path: "add-records",
                component: AddRecordsComponent
            },
            {
                path: "view-reports",
                component: ViewReportsComponent
            },
            {
                path: "view-doctors",
                component: DoctorsComponent
            },
            {
                path: "settings",
                component: SettingsComponent
            },
            {
                path: "",
                component: DashboardComponent
            },
        ]
    },
];
