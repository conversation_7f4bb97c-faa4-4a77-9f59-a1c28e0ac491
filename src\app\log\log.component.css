

/* Modern Login Page Styles */
.login-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: white;
  font-family: 'Poppins', sans-serif;
}

.login-content {
  display: flex;
  width: 85%;
  max-width: 1100px;
  height: 600px;
  background-color: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.37);
}

/* Image Side */
.login-image {
  flex: 1.2;
  position: relative;
  overflow: hidden;
}

.login-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.8s ease;
}

.login-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(56, 130, 62, 0.3), rgba(128, 205, 132, 0.1));
}

.login-content:hover .login-image img {
  transform: scale(1.05);
}

/* Form Side */
.login-form-container {
  flex: 1;
  padding: 3.5rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.login-header {
  margin-bottom: 2.5rem;
  text-align: center;
}

.logo-container {
  display: inline-block;
  background: linear-gradient(135deg, #38823E, #80CD84);
  padding: 0.8rem 1.5rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  box-shadow: 0 8px 15px rgba(56, 130, 62, 0.2);
  transform: perspective(1px) translateZ(0);
  transition: transform 0.3s;
}

.logo-container:hover {
  transform: scale(1.05);
}

.logo-text {
  color: white;
  margin: 0;
  font-weight: 700;
  letter-spacing: 1px;
  font-size: 1.5rem;
}

h2 {
  color: #333;
  font-weight: 700;
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
}

.subtitle {
  color: #888;
  font-size: 0.95rem;
  margin-bottom: 0;
}

/* Form Elements */
form {
  width: 100%;
}

.form-group {
  margin-bottom: 1.8rem;
}

label {
  display: block;
  color: #555;
  font-weight: 500;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.input-container svg {
  position: absolute;
  left: 15px;
  color: #aaa;
}

input {
  width: 100%;
  padding: 15px 15px 15px 45px;
  border: 1px solid #eee;
  border-radius: 12px;
  background-color: #f9f9f9;
  font-size: 0.95rem;
  transition: all 0.3s;
}

input:focus {
  outline: none;
  border-color: #38823E;
  background-color: white;
  box-shadow: 0 0 0 4px rgba(56, 130, 62, 0.1);
}

input::placeholder {
  color: #bbb;
}

.role-detected {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #38823E;
  font-size: 0.85rem;
  margin-top: 0.6rem;
  animation: fadeIn 0.4s ease;
}

.role-detected svg {
  color: #38823E;
}

/* Login Button */
.login-button {
  width: 100%;
  padding: 15px;
  border: none;
  border-radius: 12px;
  background: linear-gradient(135deg, #38823E, #4CAF50);
  color: white;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 15px rgba(56, 130, 62, 0.2);
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.7s ease;
}

.login-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(56, 130, 62, 0.3);
  background: linear-gradient(135deg, #2d6a32, #3e8e41);
}

.login-button:hover::before {
  left: 100%;
}

.login-button:disabled {
  background: #cccccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.login-button:disabled::before {
  display: none;
}

/* Spinner */
.spinner {
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 0.8s linear infinite;
}

/* Error Message */
.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #fff0f0;
  color: #e74c3c;
  padding: 12px 15px;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
  border-left: 4px solid #e74c3c;
  animation: shake 0.5s ease-in-out;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .login-content {
    width: 90%;
    height: auto;
  }

  .login-form-container {
    padding: 2.5rem;
  }
}

@media (max-width: 768px) {
  .login-content {
    flex-direction: column;
    max-width: 450px;
  }

  .login-image {
    height: 200px;
  }

  .login-form-container {
    padding: 2rem;
  }

  h2 {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 1rem;
  }

  .login-content {
    width: 100%;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
  }

  .login-form-container {
    padding: 1.5rem;
  }

  .login-image {
    height: 150px;
  }
}

/* Add this to your existing CSS */

.signup-link {
  text-align: center;
  margin-top: 1.5rem;
  font-size: 0.9rem;
  color: #666;
}

.signup-link a {
  color: #38823E;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s;
  position: relative;
}

.signup-link a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 1px;
  bottom: -2px;
  left: 0;
  background-color: #38823E;
  transition: width 0.3s ease;
}

.signup-link a:hover {
  color: #2d6a32;
}

.signup-link a:hover::after {
  width: 100%;
}
