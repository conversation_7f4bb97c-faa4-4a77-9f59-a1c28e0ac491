import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';
import { LoginService } from '../service/reportsAndRecords/auth/login.service';
import { LoginRequest, UserRole } from '../models/auth/login.model';

@Component({
  selector: 'app-log',
  standalone: true,
  imports: [FormsModule, CommonModule,RouterLink],
  templateUrl: './log.component.html',
  styleUrl: './log.component.css'
})
export class LogComponent {
  username: string = '';
  password: string = '';
  detectedRole: UserRole | null = null;
  isLoading: boolean = false;
  errorMessage: string = '';

  // Map of usernames to roles for demonstration
  private userRoleMap: { [key: string]: UserRole } = {
    'doctor': UserRole.DOCTOR,
    'doctor1': UserRole.DOCTOR,
    'doctor2': UserRole.DOCTOR,
    'opd': UserRole.OPD_ADMIN,
    'opdadmin': UserRole.OPD_ADMIN,
    'prescription': UserRole.PRESCRIPTION_ADMIN,
    'prescriptionadmin': UserRole.PRESCRIPTION_ADMIN
  };

  constructor(
    private router: Router,
    private loginService: LoginService
  ) {}

  detectRole(): void {
    const lowerUsername = this.username.toLowerCase();

    // Check if username contains role keywords
    if (lowerUsername.includes('doctor')) {
      this.detectedRole = UserRole.DOCTOR;
    } else if (lowerUsername.includes('opd')) {
      this.detectedRole = UserRole.OPD_ADMIN;
    } else if (lowerUsername.includes('prescription')) {
      this.detectedRole = UserRole.PRESCRIPTION_ADMIN;
    } else if (this.userRoleMap[lowerUsername]) {
      // Check predefined username-role mapping
      this.detectedRole = this.userRoleMap[lowerUsername];
    } else {
      this.detectedRole = null;
    }
  }

  getRoleDisplay(role: UserRole): string {
    switch(role) {
      case UserRole.DOCTOR:
        return 'Doctor';
      case UserRole.OPD_ADMIN:
        return 'OPD Admin';
      case UserRole.PRESCRIPTION_ADMIN:
        return 'Prescription Admin';
      default:
        return 'Unknown';
    }
  }

  onSubmit() {
    if (!this.detectedRole) {
      this.errorMessage = 'Unable to determine role from username';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';

    const loginRequest: LoginRequest = {
      username: this.username,
      password: this.password,
      role: this.detectedRole
    };

    // Use mockLogin for development, replace with login for production
    this.loginService.mockLogin(loginRequest).subscribe({
      next: (response) => {
        this.isLoading = false;
        if (response.success) {
          // Navigate based on role
          this.navigateByRole(this.detectedRole as UserRole);
        } else {
          this.errorMessage = response.message || 'Login failed';
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.errorMessage = error.message || 'An error occurred during login';
        console.error('Login error:', error);
      }
    });
  }

  private navigateByRole(role: UserRole): void {
    switch(role) {
      case UserRole.DOCTOR:
        this.router.navigate(['/dashboard']);
        break;
      case UserRole.OPD_ADMIN:
        this.router.navigate(['/appointment-handling']);
        break;
      case UserRole.PRESCRIPTION_ADMIN:
        this.router.navigate(['/prescription-management']);
        break;
      default:
        this.router.navigate(['/dashboard']);
    }
  }
}
