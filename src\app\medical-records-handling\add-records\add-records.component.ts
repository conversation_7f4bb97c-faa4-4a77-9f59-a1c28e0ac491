import {CommonModule, NgFor} from '@angular/common';
import {HttpClient, HttpErrorResponse} from '@angular/common/http';
import {Component, OnInit, ViewChild, viewChild} from '@angular/core';
import {ReportService} from '../../service/reportsAndRecords/report.service';
import {Report} from '../../service/reportsAndRecords/report-record-env.service';
import {FormsModule, NgForm} from '@angular/forms';
import {RecordService} from '../../service/reportsAndRecords/record.service';
import {RouterLink} from '@angular/router';
import { AddReportComponent } from "../add-report/add-report.component";
import e from 'express';
import { IncomingMessage } from 'http';

@Component({
  selector: 'app-add-records',
  standalone: true,
  imports: [NgFor, FormsModule, CommonModule, RouterLink, AddReportComponent],
  templateUrl: './add-records.component.html',
  styleUrl: './add-records.component.css',
})
export class AddRecordsComponent implements OnInit {
  checkIfAdded(currentReport: Report): boolean {
    return this.addedReportList.some(report => report.reportId === currentReport.reportId);
  }

  getThis(): AddRecordsComponent {
    return this;
  }

  @ViewChild(AddReportComponent) addReportComp!: AddReportComponent;
  
  //Records section
  displayAddReports: boolean = false;
  public addedReportList: Report[] = [];
  public reportNames: string = '';
  public prescriptionList: any[] = [];
  public prescriptionNames: string = '';
  public recordId: any;
  public searchedPatient: any = null;
  public currentRecord: any = null;
  public saveReports: any[] = [];

  public allReportList!: Report[];
  public allPrescriptionList: any[] = [];
  public displayPrescriptionList: any[] = [];

  // Add a patient details array
  public patient: any = {
    id: '',
    name: '',
    gender: '',
    age: '',
    contact: '',
  }

  public pre_report: {
    'reportId': string;
    'patientId': string;
    'reportLink': string;
    'categoryType': string;
    'reportDate': Date;
    'note': string;
    recordList: any[];
  } | undefined;


  displayReportList!: Report[];
  recordDescription: string = "Detailed medical record";
  patientIdToPayLoad!: string;


  constructor(
    private http: HttpClient,
    private reportService: ReportService,
    private recordService: RecordService
  ) {
    this.loadReports()
  }

  ngOnInit(): void {
    this.clearAllFields();
    this.currentRecord = this.recordService.getCurrentRecord();

    if (this.currentRecord === null) {
      this.http
        .get('http://localhost:8083/record/get-nextId', {responseType: 'text'})
        .subscribe({
          next: (response: string) => {
            this.recordId = response;
          },
          error: (err) => console.error('Failed to fetch record ID:', err),
        });
      this.http
        .get('http://localhost:8082/api/v1/prescription/all', {responseType: 'text'})
        .subscribe({
          next: (response: any) => {
            this.allPrescriptionList = response;
          },
          error: (err) => console.error('Failed to fetch record ID:', err),
        });
      this.displayPrescriptionList = this.allPrescriptionList;
    } else {
      console.log('Current record:', this.currentRecord);

      if (this.currentRecord.patientID) {
        this.searchedPatient = this.currentRecord.patientID;
        this.patientIdToPayLoad = this.currentRecord.patientID;
        this.searchPatientById(this.patientIdToPayLoad);
      } else {
        console.error('Current record does not have a valid patientId');
      }

      this.recordId = this.currentRecord.recordId || '';
      this.prescriptionList = this.currentRecord.reportList
        ? this.currentRecord.reportList.filter((report: Report) => report.reportId.startsWith('PRP'))
        : [];
      this.addedReportList = this.currentRecord.reportList
        ? this.currentRecord.reportList.filter((report: Report) => !report.reportId.startsWith('PRP'))
        : [];
      this.recordDescription = this.currentRecord.description || '';

    }
  }

  //For storing display Reports and Prescriptions
  // public displayReportList = this.allReportList;

  searchReportsByDate(event: Event) {
    let date: string = (event.target as HTMLInputElement).value;
    if (date.length > 0) {
      //Change display lists to filtered by date list
      this.displayReportList = this.allReportList.filter(
        (element) => element.date == date
      );
      this.displayPrescriptionList = this.allPrescriptionList.filter(
        (element) => element.issuedDate == date
      );
    } else {
      //Display lists back to all list
      this.displayReportList = this.allReportList;
      this.displayPrescriptionList = this.allPrescriptionList;
    }
  }


  loadReports() {
    this.reportService.getAllReports().subscribe({
      next: data => {
        this.allReportList = data.map(report => ({
          ...report,
          isChecked: false
        }));
      }
    })

  }

  addReport(inComingReport: Report, event: Event) {
    if (this.addedReportList.some(r => r.reportId === inComingReport.reportId)) {
      alert("report is already added")
      const checkbox = event.target as HTMLInputElement;
      checkbox.checked = true;
      return;
    } else {
      this.addedReportList.push(inComingReport)
    }
  }

  removeFromAddedReports(report: Report) {
    const index = this.addedReportList.indexOf(report);
    if (index > -1) {
      this.addedReportList.splice(index, 1);
    }
    this.removeCheckFromAllList(report.reportId)
  }

  //removing check from the all report/prescription list just after clinking remove (x) button in the added report list/added prescription list;
  removeCheckFromAllList(reportId: string) {
    const element = document.getElementById(reportId);
    if (element) {
      const checkbox = element.querySelector('input[type="checkbox"]') as HTMLInputElement;
      if (checkbox) {
        checkbox.checked = false; // Uncheck the checkbox
        // Update the corresponding object's `isChecked` property
        const report = this.allReportList.find(r => r.reportId === reportId);
        if (report) {
          report.isChecked = false;
        }
      }
    }
  }

  //changes added again
  submitCreateRecordForm(form: NgForm) {
    console.log(form.value);
  }

  addPrescription(prescription: any, event: Event) {
    const isChecked = (event.target as HTMLInputElement).checked;

    if (isChecked) {
      // Add the prescription to the list
      this.pre_report = {
        reportId: 'PRP' + this.searchedPatient.slice(-4).padStart(4, '0') + prescription.id.slice(-3).padStart(3, '0'),
        patientId: prescription.patientID,
        reportLink: prescription.prescriptionPath,
        categoryType: 'Prescription Report',
        reportDate: prescription.issuedDate,
        note: 'This is a prescription',
        recordList: []
      }

      this.prescriptionList.push(this.pre_report);
      this.prescriptionNames += this.pre_report.reportId + '\n';
    } else {
      // Remove the prescription from the list
      this.prescriptionList = this.prescriptionList.filter(
        (p) => p.reportId !== 'PRP' + this.searchedPatient.slice(-4).padStart(4, '0') + prescription.id.slice(-3).padStart(3, '0')
      );

      this.prescriptionNames = this.prescriptionList
        .map((p) => p.reportId)
        .join('\n');
    }
  }

  removeFromPrescriptions(prescription: any) {
    const index = this.prescriptionList.indexOf(prescription);
    if (index > -1) {
      this.prescriptionList.splice(index, 1);
    }
    this.removeCheckFromAllList(prescription.id)
  }

  parseStringToId(formattedId: string): number {
    if (!formattedId.startsWith('P') || formattedId.length < 5) {
      throw new Error("Invalid format. Expected format is 'Pxxxx'.");
    }
    return Number(formattedId.slice(1));
  }

  searchPatientById(patientId: string) {
    if (!patientId.trim()) {
      alert('Please enter a Patient ID.');
      return;
    }

    let id: number;

    try {
      id = this.parseStringToId(patientId.trim());
      console.log("Parsed ID:", id);
    } catch (error) {
      alert('Invalid Patient ID format. Please check and use like "Pxxxx".');
      return;
    }


    this.http.get(`http://localhost:8081/patient/patient-search-by-id/${id}`).subscribe({
      next: (response: any) => {

        this.patient = {
          id: response.id,
          name: `${response.firstName} ${response.lastName}`,
          age: response.age,
          gender: response.gender,
          contact: response.contactNo,
        };

        this.searchedPatient = this.patient;
        this.displayPrescriptionList = this.allPrescriptionList.filter(
          (element) => element.patientID == this.searchedPatient.id
        );

        document.querySelector('.patient-info')?.removeAttribute('style');
      },
      error: (err) => {

        console.error('Failed to search patientId:', err);
        alert('Patient not found.');
        this.searchedPatient = null;

        document.querySelector('.patient-info')?.setAttribute('style', 'display: none;');
      },
    });
  }


  generateRandomId() {
    return Math.floor(Math.random() * (100000 + 1)) + 1000;
  }

  saveRecord() {
    if (this.addedReportList.length <= 0) {
      alert("please add reports to the record...!");
      return;
    }
    if (this.patientIdToPayLoad == "") {
      alert("please add patient ID...!");
      return;
    }
    this.addedReportList.forEach((report) => {
      this.saveReports.push(report);
    });
    this.prescriptionList.forEach((report) => {
      this.saveReports.push(report);
    });
    const payload = {
      recordId: "R" + this.generateRandomId(),
      patientID: this.patientIdToPayLoad ? this.patientIdToPayLoad : this.addedReportList[0].patientId,
      recordDate: new Date().toISOString().split('T')[0],
      description: this.recordDescription,
      reportList: this.saveReports
    };

    console.log('Sending payload:', payload); // test log

    if (this.currentRecord === null) {

      this.http
        .post('http://localhost:8083/record/add-record', payload)
        .subscribe({
          next: (response) => {
            alert('Record saved successfully: ' + response);
            this.clearAllFields();
          },
          error: (error: HttpErrorResponse) => {
            if (error.status === 200) {
              alert('Record saved successfully: ');
              this.clearAllFields();
            } else {
              alert("Error Saving Record :" + error.message);
            }
          },
        });
    } else {
      this.http
        .put('http://localhost:8083/update-record', payload)
        .subscribe({
          next: (response) => {
            alert('Record updated successfully: ' + response);
            this.clearAllFields();
          },
          error: (error: HttpErrorResponse) => {
            alert("Error Updating Record: " + error.message);
          },
        });
    }

  }

  parseStringToNumber(patientId: string): number {
    // Extract the numeric part of the patient ID using a regular expression.
    const match = patientId.match(/\d+/);
    if (!match) {
      throw new Error('Invalid Patient ID format.');
    }
    return parseInt(match[0], 10); // Convert the extracted numeric string to a number.
  }

  clearCheckBox() {
    this.prescriptionList.forEach(prescription => prescription.isChecked = false);
    this.addedReportList.forEach(report => report.isChecked = false);
  }

  clearAllFields() {
    this.clearCheckBox();
    this.addedReportList = [];
    this.prescriptionList = [];
    this.reportNames = '';
    this.prescriptionNames = '';
    (document.getElementById('note') as HTMLTextAreaElement).value = '';
    this.searchedPatient = null;
    this.recordDescription = "Detailed medical record";
  }
  showDisplayAddReports() {
    this.addReportComp.toggleAddReportDisplay();
    this.loadReports();
  }
}
