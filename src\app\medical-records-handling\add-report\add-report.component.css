.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1200;
    backdrop-filter: blur(1px);
    background-color: rgba(255, 255, 255, 0.272);
    /* Semi-transparent white */
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Card styling */
.report-card {
    background-color: #58b368;
    /* Green color */
    border-radius: 10px;
    padding: 20px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    position: relative;
}

/* Close button styling */
.close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: transparent;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
}

/* Button styling */
.btn-success {
    background-color: #2c974b;
    border-color: #2c974b;
    font-weight: bold;
}

.btn-success:hover {
    background-color: #217a37;
    border-color: #217a37;
}
