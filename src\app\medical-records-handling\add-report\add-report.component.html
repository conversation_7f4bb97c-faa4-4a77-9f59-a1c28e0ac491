<div class="overlay" [class.d-none]="!display">
    <div class="card report-card">
        <button class="btn-close close-btn" (click)="toggleAddReportDisplay()" aria-label="Close">x</button>
        <h3 class="text-center">ADD REPORT</h3>
        <form #f="ngForm" (submit)="submitReport(f)">
            <div class="mb-3">
                <label for="report-note" class="form-label">Enter the note about report</label>
                <textarea class="form-control" [(ngModel)]="note" id="note" name="note" rows="3"></textarea>
            </div>
            <div class="mb-3">
                <label for="upload-file" class="form-label">Medical Report</label>
                <input class="form-control" ngModel (change)="onFileSelected($event)" type="file" id="report"
                    name="report" accept="application/pdf">
            </div>
            <button type="submit" class="btn btn-success w-100">Upload Report</button>
        </form>
    </div>
</div>