export interface User {
  id?: string;  username: string;
  role: UserR<PERSON>;  token?: string;
}
export interface LoginRequest {  username: string;
  password: string;  role: UserRole;
}
export interface LoginResponse {  user: User;
  token: string;  success: boolean;
  message?: string;}
export enum UserRole {
  DOCTOR = 'doctor',  OPD_ADMIN = 'opdAdmin',
  PRESCRIPTION_ADMIN = 'prescriptionAdmin'
}













