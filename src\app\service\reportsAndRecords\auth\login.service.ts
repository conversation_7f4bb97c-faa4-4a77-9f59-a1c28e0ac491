

import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { BehaviorSubject, Observable, catchError, map, tap, throwError } from 'rxjs';
import { LoginRequest, LoginResponse, User, UserRole } from '../../../models/auth/login.model';
import { Router } from '@angular/router';
import { environment } from '../../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class LoginService {
  private currentUserSubject: BehaviorSubject<User | null>;
  public currentUser: Observable<User | null>;
  private readonly AUTH_URL = `${environment.PATIENT_MANAGEMENT_BASE_URL}/auth`;
  private readonly USER_KEY = 'currentUser';

  constructor(private http: HttpClient, private router: Router) {
    const storedUser = localStorage.getItem(this.USER_KEY);
    this.currentUserSubject = new BehaviorSubject<User | null>(
      storedUser ? JSON.parse(storedUser) : null
    );
    this.currentUser = this.currentUserSubject.asObservable();
  }

  public get currentUserValue(): User | null {
    return this.currentUserSubject.value;
  }

  login(loginRequest: LoginRequest): Observable<LoginResponse> {
    return this.http.post<LoginResponse>(`${this.AUTH_URL}/login`, loginRequest)
      .pipe(
        tap(response => {
          if (response.success && response.user && response.token) {
            // Store user details and token in local storage
            const user: User = {
              ...response.user,
              token: response.token
            };
            localStorage.setItem(this.USER_KEY, JSON.stringify(user));
            this.currentUserSubject.next(user);
          }
        }),
        catchError(this.handleError)
      );
  }

  logout(): void {
    // Remove user from local storage
    localStorage.removeItem(this.USER_KEY);
    this.currentUserSubject.next(null);
    this.router.navigate(['/login']);
  }

  isAuthenticated(): boolean {
    return !!this.currentUserValue;
  }

  hasRole(role: UserRole): boolean {
    return this.currentUserValue?.role === role;
  }

  // For demo/development purposes only - remove in production
  mockLogin(loginRequest: LoginRequest): Observable<LoginResponse> {
    // Simulate API response
    const mockResponse: LoginResponse = {
      user: {
        id: '123',
        username: loginRequest.username,
        role: loginRequest.role
      },
      token: 'mock-jwt-token',
      success: true
    };

    // Store user details and token in local storage
    localStorage.setItem(this.USER_KEY, JSON.stringify({
      ...mockResponse.user,
      token: mockResponse.token
    }));

    this.currentUserSubject.next(mockResponse.user);
    return new Observable(observer => {
      setTimeout(() => {
        observer.next(mockResponse);
        observer.complete();
      }, 500); // Simulate network delay
    });
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error
      errorMessage = error.error?.message || `Error Code: ${error.status}, Message: ${error.message}`;
    }

    return throwError(() => new Error(errorMessage));
  }
}
