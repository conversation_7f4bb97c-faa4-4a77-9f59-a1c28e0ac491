import { Injectable } from '@angular/core';
import { Report, ReportRecordEnvService } from './report-record-env.service';
import { catchError, Observable, throwError } from 'rxjs';
import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class ReportService {
  mainUrl = "";
  constructor(private ReportRecordEnvService: ReportRecordEnvService, private http: HttpClient) {
    this.mainUrl = ReportRecordEnvService.MAIN_URL;
  }

  // Search report by ID
  searchById(id: string): Observable<Report[]> {
    return this.http.get<Report[]>(`${this.mainUrl}/report/searchById/${id}`);
  }

  // Search report by ID and Date
  searchByIdAndDate(id: string, date: string): Observable<Report[]> {
    return this.http.get<Report[]>(`${this.mainUrl}/report/searchByIdAndDate`, {
      params: { id, date }
    });
  }

  // Search report by Date
  searchByDate(date: string): Observable<Report[]> {
    return this.http.get<Report[]>(`${this.mainUrl}/report/searchByDate`, {
      params: { date }
    });
  }

  addReport(note: string, report: File): Observable<any> {
    const formData = new FormData();
    formData.append('note', note); // Add the note
    formData.append('report', report); // Add the file

    // Send POST request
    return this.http.post(this.mainUrl + "/report/add", formData, {
      responseType: 'text' // Expecting a string response
    }).pipe(
      catchError(this.handleError)
    );
  }

  // Error handling
  private handleError(error: HttpErrorResponse): Observable<never> {
    console.error('Error occurred========:', error);
    return throwError(error.message || 'Server error');
  }

  // Download a report
  downloadReport(fileName: string): void {
    let downloadLink = (this.mainUrl + "/" + fileName);
    console.log("final links : " + downloadLink);
    this.http.get(downloadLink, { responseType: 'blob' }).subscribe({
      next: (response: Blob) => {
        // Create a temporary URL for the blob
        const blobUrl = window.URL.createObjectURL(response);

        // Create an anchor element and trigger a download
        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = fileName; // Use the original file name for the download
        link.click();

        // Clean up the URL object
        window.URL.revokeObjectURL(blobUrl);
      },
      error: (error) => {
        console.error('Download failed: ', error);
      }
    });
  }


  // Get all reports
  getAllReports(): Observable<Report[]> {
    return this.http.get<Report[]>(`${this.mainUrl}/report/all`);
  }

  // Get report by ID
  getReportById(reportId: string): Observable<Report> {
    return this.http.get<Report>(`${this.mainUrl}/report/${reportId}`);
  }

  // Delete a report
  deleteReport(reportId: string): Observable<any> {
    console.log("delete report : " + reportId)
    return this.http.delete(`${this.mainUrl}/report/delete/${reportId}`);
  }

}


export { Report };