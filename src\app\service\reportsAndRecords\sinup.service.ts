import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { delay, tap } from 'rxjs/operators';
import { SignupRequest, SignupResponse } from '../../models/sinup.model';

@Injectable({
  providedIn: 'root'
})
export class SinupService {
  constructor() { }

  signup(request: SignupRequest): Observable<SignupResponse> {
    // This is a mock implementation
    // In a real app, you would make an HTTP request to your backend
    console.log('Signup request:', request);

    // Simulate API call with delay
    return of({
      success: true,
      message: 'Signup successful'
    }).pipe(
      delay(1500), // Simulate network delay
      tap(response => console.log('Signup response:', response))
    );
  }

}
