<div class="signup-container">
  <div class="signup-card">
    <div class="signup-header">
      <h2>Create Account</h2>
      <p>Join our healthcare management system</p>
    </div>

    <div *ngIf="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>

    <form (ngSubmit)="onSubmit()" #signupForm="ngForm">
      <!-- Username -->
      <div class="form-group">
        <label for="username">Username</label>
        <div class="input-container">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path d="M8 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm2-3a2 2 0 1 1-4 0 2 2 0 0 1 4 0zm4 8c0 1-1 1-1 1H3s-1 0-1-1 1-4 6-4 6 3 6 4zm-1-.004c-.001-.246-.154-.986-.832-1.664C11.516 10.68 10.289 10 8 10c-2.29 0-3.516.68-4.168 1.332-.678.678-.83 1.418-.832 1.664h10z"/>
          </svg>
          <input
            type="text"
            id="username"
            [(ngModel)]="signupData.username"
            name="username"
            placeholder="Enter your username"
            required
            [disabled]="isLoading">
        </div>
      </div>

      <!-- Email -->
      <div class="form-group">
        <label for="email">Email</label>
        <div class="input-container">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4zm2-1a1 1 0 0 0-1 1v.217l7 4.2 7-4.2V4a1 1 0 0 0-1-1H2zm13 2.383-4.708 2.825L15 11.105V5.383zm-.034 6.876-5.64-3.471L8 9.583l-1.326-.795-5.64 3.47A1 1 0 0 0 2 13h12a1 1 0 0 0 .966-.741zM1 11.105l4.708-2.897L1 5.383v5.722z"/>
          </svg>
          <input
            type="email"
            id="email"
            [(ngModel)]="signupData.email"
            name="email"
            placeholder="Enter your email"
            required
            [disabled]="isLoading">
        </div>
      </div>

      <!-- Phone Number -->
      <div class="form-group">
        <label for="phoneNumber">Phone Number</label>
        <div class="input-container">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path d="M3.654 1.328a.678.678 0 0 0-1.015-.063L1.605 2.3c-.483.484-.661 1.169-.45 1.77a17.568 17.568 0 0 0 4.168 6.608 17.569 17.569 0 0 0 6.608 4.168c.601.211 1.286.033 1.77-.45l1.034-1.034a.678.678 0 0 0-.063-1.015l-2.307-1.794a.678.678 0 0 0-.58-.122l-2.19.547a1.745 1.745 0 0 1-1.657-.459L5.482 8.062a1.745 1.745 0 0 1-.46-1.657l.548-2.19a.678.678 0 0 0-.122-.58L3.654 1.328zM1.884.511a1.745 1.745 0 0 1 2.612.163L6.29 2.98c.329.423.445.974.315 1.494l-.547 2.19a.678.678 0 0 0 .178.643l2.457 2.457a.678.678 0 0 0 .644.178l2.189-.547a1.745 1.745 0 0 1 1.494.315l2.306 1.794c.829.645.905 1.87.163 2.611l-1.034 1.034c-.74.74-1.846 1.065-2.877.702a18.634 18.634 0 0 1-7.01-4.42 18.634 18.634 0 0 1-4.42-7.009c-.362-1.03-.037-2.137.703-2.877L1.885.511z"/>
          </svg>
          <input
            type="tel"
            id="phoneNumber"
            [(ngModel)]="signupData.phoneNumber"
            name="phoneNumber"
            placeholder="Enter your phone number"
            required
            [disabled]="isLoading">
        </div>
      </div>

      <!-- Role Selection -->
      <div class="form-group">
        <label for="role">Select Role</label>
        <div class="role-selection">
          <div class="role-option"
               [class.selected]="signupData.role === 'doctor'"
               (click)="signupData.role = 'doctor'; onRoleChange()">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16">
              <path d="M8 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6Zm2-3a2 2 0 1 1-4 0 2 2 0 0 1 4 0Zm4 8c0 1-1 1-1 1H3s-1 0-1-1 1-4 6-4 6 3 6 4Zm-1-.004c-.001-.246-.154-.986-.832-1.664C11.516 10.68 10.289 10 8 10c-2.29 0-3.516.68-4.168 1.332-.678.678-.83 1.418-.832 1.664h10Z"/>
            </svg>
            <span>Doctor</span>
          </div>
          <div class="role-option"
               [class.selected]="signupData.role === 'opdAdmin'"
               (click)="signupData.role = 'opdAdmin'; onRoleChange()">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16">
              <path d="M8.5 5.5a.5.5 0 0 0-1 0v3.362l-1.429 2.38a.5.5 0 1 0 .858.515l1.5-2.5A.5.5 0 0 0 8.5 9V5.5z"/>
              <path d="M6.5 0a.5.5 0 0 0 0 1H7v1.07a7.001 7.001 0 0 0-3.273 12.474l-.602.602a.5.5 0 0 0 .707.708l.746-.746A6.97 6.97 0 0 0 8 16a6.97 6.97 0 0 0 3.422-.892l.746.746a.5.5 0 0 0 .707-.708l-.601-.602A7.001 7.001 0 0 0 9 2.07V1h.5a.5.5 0 0 0 0-1h-3zm1.038 3.018a6.093 6.093 0 0 1 .924 0 6 6 0 1 1-.924 0zM0 3.5c0 .753.333 1.429.86 1.887A8.035 8.035 0 0 1 4.387 1.86 2.5 2.5 0 0 0 0 3.5zM13.5 1c-.753 0-1.429.333-1.887.86a8.035 8.035 0 0 1 3.527 3.527A2.5 2.5 0 0 0 13.5 1z"/>
            </svg>
            <span>OPD Admin</span>
          </div>
          <div class="role-option"
               [class.selected]="signupData.role === 'prescriptionAdmin'"
               (click)="signupData.role = 'prescriptionAdmin'; onRoleChange()">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16">
              <path d="M8 1a2.5 2.5 0 0 1 2.5 2.5V4h-5v-.5A2.5 2.5 0 0 1 8 1zm3.5 3v-.5a3.5 3.5 0 1 0-7 0V4H1v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V4h-3.5zM2 5h12v9a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V5z"/>
            </svg>
            <span>Prescription Admin</span>
          </div>
        </div>
      </div>

      <!-- Specialization (only for doctors) -->
      <div class="form-group" *ngIf="showSpecialization">
        <label for="specialization">Specialization</label>
        <div class="input-container">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
          </svg>
          <select
            id="specialization"
            [(ngModel)]="signupData.specialization"
            name="specialization"
            required
            [disabled]="!showSpecialization">
            <option value="" disabled selected>Select your specialization</option>
            <option *ngFor="let spec of specializations" [value]="spec">{{ spec }}</option>
          </select>
        </div>
      </div>

      <!-- Password -->
      <div class="form-group">
        <label for="password">Password</label>
        <div class="input-container">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path d="M8 1a2.5 2.5 0 0 1 2.5 2.5V4h-5v-.5A2.5 2.5 0 0 1 8 1zm3.5 3v-.5a3.5 3.5 0 1 0-7 0V4H1v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V4h-3.5zM2 5h12v9a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V5z"/>
          </svg>
          <input
            type="password"
            id="password"
            [(ngModel)]="signupData.password"
            name="password"
            placeholder="Enter your password"
            required
            [disabled]="isLoading">
        </div>
      </div>

      <!-- Submit Button -->
      <div class="form-group">
        <button type="submit" [disabled]="!signupForm.valid || isLoading">
          {{ isLoading ? 'Creating Account...' : 'Create Account' }}
        </button>
      </div>
    </form>
  </div>
</div>
