import { Component, inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';
import { UserRole } from '../models/auth/login.model';
import { SignupRequest } from '../models/sinup.model';
import { SinupService } from '../service/reportsAndRecords/sinup.service';

@Component({
  selector: 'app-sinup',
  standalone: true,
  imports: [FormsModule, CommonModule, RouterLink],
  templateUrl: './sinup.component.html',
  styleUrl: './sinup.component.css'
})
export class SinupComponent {
  signupData: SignupRequest = {
    username: '',
    email: '',
    phoneNumber: '',
    password: '',
    role: UserRole.DOCTOR,
    specialization: ''
  };

  confirmPassword: string = '';
  showSpecialization: boolean = true;
  isLoading: boolean = false;
  errorMessage: string = '';

  // List of specializations for doctors
  specializations: string[] = [
    'Cardiology',
    'Dermatology',
    'Endocrinology',
    'Gastroenterology',
    'Neurology',
    'Oncology',
    'Pediatrics',
    'Psychiatry',
    'Radiology',
    'Surgery'
  ];

  private router = inject(Router);
  private sinupService = inject(SinupService);

  onRoleChange(): void {
    // Show specialization field only for doctors
    this.showSpecialization = this.signupData.role === UserRole.DOCTOR;

    // Clear specialization if not a doctor
    if (!this.showSpecialization) {
      this.signupData.specialization = '';
    }
  }

  onSubmit(): void {
    if (this.signupData.password !== this.confirmPassword) {
      this.errorMessage = 'Passwords do not match';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';

    this.sinupService.signup(this.signupData).subscribe({
      next: (response) => {
        this.isLoading = false;
        if (response.success) {
          this.router.navigate(['/log']);
        } else {
          this.errorMessage = response.message || 'Signup failed';
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.errorMessage = error.message || 'An error occurred during signup';
      }
    });
  }

  getRoleDisplay(role: UserRole): string {
    switch(role) {
      case UserRole.DOCTOR:
        return 'Doctor';
      case UserRole.OPD_ADMIN:
        return 'OPD Admin';
      case UserRole.PRESCRIPTION_ADMIN:
        return 'Prescription Admin';
      default:
        return 'Unknown';
    }
  }
}
